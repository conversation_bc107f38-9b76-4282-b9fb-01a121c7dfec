name: <PERSON><PERSON><PERSON><PERSON><PERSON> AI CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.9'

jobs:
  # Frontend Tests
  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run frontend linting
      run: |
        cd frontend
        npm run lint
    
    - name: Run frontend tests
      run: |
        cd frontend
        CI=true npm run test:coverage
    
    - name: Upload frontend coverage
      uses: codecov/codecov-action@v3
      with:
        directory: ./frontend/coverage
        flags: frontend

  # Backend Tests
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: modporter
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install backend dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -e .
        pip install -r requirements.txt
    
    - name: Run backend linting
      run: |
        cd backend
        ruff check .
      continue-on-error: true

    - name: Run database migrations
      run: |
        cd backend
        PYTHONPATH=. alembic upgrade head
      env:
        DATABASE_URL: postgresql://postgres:password@localhost:5432/modporter

    - name: Run backend tests
      if: always()
      run: |
        cd backend
        PYTHONPATH=. pytest --cov=. tests/ --cov-report=xml
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:password@localhost:5432/modporter
        REDIS_URL: redis://localhost:6379
    
    - name: Upload backend coverage
      uses: codecov/codecov-action@v3
      with:
        directory: ./backend
        flags: backend

  # AI Engine Tests
  ai-engine-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install AI engine dependencies
      run: |
        cd ai-engine
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run AI engine tests
      run: |
        cd ai-engine
        PYTHONPATH=. pytest --cov=src tests/ --cov-report=xml
      env:
        # Mock API keys for testing
        OPENAI_API_KEY: "test-key"
        ANTHROPIC_API_KEY: "test-key"
    
    - name: Upload AI engine coverage
      uses: codecov/codecov-action@v3
      with:
        directory: ./ai-engine
        flags: ai-engine

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests, ai-engine-tests]
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: modporter
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install all dependencies
      run: |
        npm run install-all
        cd backend && pip install -r requirements.txt
        cd ../ai-engine && pip install -r requirements.txt
    
    - name: Build frontend
      run: |
        cd frontend
        npm run build
    
    - name: Run database migrations
      run: |
        cd backend
        PYTHONPATH=. alembic upgrade head
      env:
        DATABASE_URL: postgresql://postgres:password@localhost:5432/modporter
    
    - name: Run integration tests
      run: |
        # Start backend in background
        cd backend && python main.py &
        
        # Wait for backend to start
        sleep 10
        
        # Run integration tests
        cd backend && python -m pytest tests/integration/ -v
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:password@localhost:5432/modporter
        REDIS_URL: redis://localhost:6379
        API_URL: http://localhost:8000

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Deploy (only on main branch)
  build-and-deploy:
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push Docker images
      run: |
        # Build frontend image
        docker build -t ghcr.io/${{ github.repository }}/frontend:latest ./frontend
        docker push ghcr.io/${{ github.repository }}/frontend:latest
        
        # Build backend image  
        docker build -t ghcr.io/${{ github.repository }}/backend:latest ./backend
        docker push ghcr.io/${{ github.repository }}/backend:latest
        
        # Build AI engine image
        docker build -t ghcr.io/${{ github.repository }}/ai-engine:latest ./ai-engine
        docker push ghcr.io/${{ github.repository }}/ai-engine:latest